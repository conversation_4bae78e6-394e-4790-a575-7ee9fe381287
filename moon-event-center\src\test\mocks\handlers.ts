import { http, HttpResponse } from 'msw'

export const handlers = [
  // Mock authentication endpoints
  http.post('/api/auth/login', async ({ request }) => {
    const { email, password } = await request.json() as { email: string; password: string }
    
    if (email === '<EMAIL>' && password === 'password') {
      return HttpResponse.json({
        user: {
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'customer'
        },
        token: 'mock-jwt-token'
      })
    }
    
    return HttpResponse.json(
      { error: 'Invalid credentials' },
      { status: 401 }
    )
  }),

  http.post('/api/auth/register', async ({ request }) => {
    const userData = await request.json()
    
    return HttpResponse.json({
      user: {
        id: '2',
        ...userData,
        role: 'customer'
      },
      token: 'mock-jwt-token'
    })
  }),

  http.post('/api/auth/logout', () => {
    return HttpResponse.json({ success: true })
  }),

  // Mock contact form endpoint
  http.post('/api/contact', async ({ request }) => {
    const formData = await request.json()
    
    return HttpResponse.json({
      success: true,
      message: 'Contact form submitted successfully',
      id: 'contact-' + Date.now()
    })
  }),

  // Mock calendar/availability endpoint
  http.get('/api/calendar/availability', () => {
    return HttpResponse.json({
      availableDates: [
        '2024-08-15',
        '2024-08-22',
        '2024-09-05',
        '2024-09-12'
      ]
    })
  }),

  // Mock gallery images endpoint
  http.get('/api/gallery', () => {
    return HttpResponse.json({
      images: [
        {
          id: '1',
          url: '/assets/gallery/wedding1.jpg',
          category: 'wedding',
          alt: 'Beautiful wedding setup'
        },
        {
          id: '2',
          url: '/assets/gallery/corporate1.jpg',
          category: 'corporate',
          alt: 'Corporate event space'
        }
      ]
    })
  }),

  // Mock testimonials endpoint
  http.get('/api/testimonials', () => {
    return HttpResponse.json({
      testimonials: [
        {
          id: '1',
          name: 'Sarah Johnson',
          event: 'Wedding',
          rating: 5,
          comment: 'The Moon Event Center made our wedding absolutely magical!'
        },
        {
          id: '2',
          name: 'Corporate Client',
          event: 'Corporate Gala',
          rating: 5,
          comment: 'Professional service and stunning venue.'
        }
      ]
    })
  })
]
