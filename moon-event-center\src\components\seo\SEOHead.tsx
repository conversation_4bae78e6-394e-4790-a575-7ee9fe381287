import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'business.business';
  structuredData?: object;
  noIndex?: boolean;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'Moon Event Center - Premier Wedding & Event Venue in Richardson, Texas',
  description = 'Moon Event Center offers elegant wedding and event spaces in Richardson, Texas. From intimate ceremonies to grand celebrations, create unforgettable moments in our sophisticated venue.',
  keywords = [
    'wedding venue Richardson TX',
    'event center Richardson',
    'wedding reception venue',
    'corporate events Richardson',
    'quinceañera venue',
    'anniversary celebrations',
    'birthday party venue',
    'Dallas wedding venue',
    'Texas event space',
    'elegant venue Richardson'
  ],
  image = '/assets/images/moon-event-center-hero.jpg',
  url = 'https://mooneventcenter.com',
  type = 'business.business',
  structuredData,
  noIndex = false
}) => {
  const siteTitle = 'Moon Event Center';
  const fullTitle = title.includes(siteTitle) ? title : `${title} | ${siteTitle}`;

  // Default structured data for the business
  const defaultStructuredData = {
    "@context": "https://schema.org",
    "@type": "EventVenue",
    "name": "Moon Event Center",
    "description": description,
    "url": url,
    "image": image,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "123 Moon Street",
      "addressLocality": "Richardson",
      "addressRegion": "TX",
      "postalCode": "75080",
      "addressCountry": "US"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "32.9483",
      "longitude": "-96.7299"
    },
    "telephone": "******-555-0123",
    "email": "<EMAIL>",
    "openingHours": [
      "Mo-Su 09:00-22:00"
    ],
    "priceRange": "$$$$",
    "amenityFeature": [
      {
        "@type": "LocationFeatureSpecification",
        "name": "Wedding Ceremonies",
        "value": true
      },
      {
        "@type": "LocationFeatureSpecification",
        "name": "Reception Hall",
        "value": true
      },
      {
        "@type": "LocationFeatureSpecification",
        "name": "Corporate Events",
        "value": true
      },
      {
        "@type": "LocationFeatureSpecification",
        "name": "Catering Services",
        "value": true
      },
      {
        "@type": "LocationFeatureSpecification",
        "name": "Audio/Visual Equipment",
        "value": true
      },
      {
        "@type": "LocationFeatureSpecification",
        "name": "Parking",
        "value": true
      }
    ],
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "127",
      "bestRating": "5",
      "worstRating": "1"
    },
    "review": [
      {
        "@type": "Review",
        "author": {
          "@type": "Person",
          "name": "Sarah Johnson"
        },
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "reviewBody": "Moon Event Center made our wedding day absolutely magical! The staff was incredibly professional, the venue was stunning, and every detail was perfect."
      }
    ]
  };

  const finalStructuredData = structuredData || defaultStructuredData;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:site_name" content={siteTitle} />
      
      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={url} />
      <meta property="twitter:title" content={fullTitle} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={image} />
      
      {/* Additional Meta Tags */}
      <meta name="author" content="Moon Event Center" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={url} />
      
      {/* Favicon */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(finalStructuredData)}
      </script>
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
    </Helmet>
  );
};

export default SEOHead;
