import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'customer' | 'staff' | 'admin';
  phone?: string;
  createdAt: string;
  lastLogin?: string;
}

export interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (userData: RegisterData) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => Promise<{ success: boolean; error?: string }>;
  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role?: 'customer' | 'staff';
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Simulate API calls - in a real app, these would be actual API endpoints
  const simulateApiCall = <T,>(data: T, delay: number = 1000): Promise<T> => {
    return new Promise((resolve) => {
      setTimeout(() => resolve(data), delay);
    });
  };

  // Check for existing session on mount
  useEffect(() => {
    const checkExistingSession = async () => {
      try {
        const savedUser = localStorage.getItem('moon_event_user');
        if (savedUser) {
          const userData = JSON.parse(savedUser);
          // In a real app, you'd validate the token with the server
          setUser(userData);
        }
      } catch (error) {
        console.error('Error checking existing session:', error);
        localStorage.removeItem('moon_event_user');
      } finally {
        setIsLoading(false);
      }
    };

    checkExistingSession();
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await simulateApiCall(null, 1500);

      // Mock user data - in a real app, this would come from the server
      const mockUser: User = {
        id: '1',
        email,
        firstName: email.includes('staff') ? 'Staff' : 'John',
        lastName: email.includes('staff') ? 'Member' : 'Doe',
        role: email.includes('staff') ? 'staff' : email.includes('admin') ? 'admin' : 'customer',
        phone: '+****************',
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString()
      };

      // Simple validation - in a real app, this would be server-side
      if (password.length < 6) {
        return { success: false, error: 'Invalid credentials' };
      }

      setUser(mockUser);
      localStorage.setItem('moon_event_user', JSON.stringify(mockUser));
      
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Login failed. Please try again.' };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await simulateApiCall(null, 1500);

      // Mock user creation
      const newUser: User = {
        id: Date.now().toString(),
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role || 'customer',
        phone: userData.phone,
        createdAt: new Date().toISOString()
      };

      setUser(newUser);
      localStorage.setItem('moon_event_user', JSON.stringify(newUser));
      
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Registration failed. Please try again.' };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('moon_event_user');
  };

  const updateProfile = async (userData: Partial<User>) => {
    if (!user) return { success: false, error: 'Not authenticated' };

    setIsLoading(true);
    try {
      // Simulate API call
      await simulateApiCall(null, 1000);

      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('moon_event_user', JSON.stringify(updatedUser));
      
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Profile update failed. Please try again.' };
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      // Simulate API call
      await simulateApiCall(null, 1000);
      
      // In a real app, this would send a reset email
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Password reset failed. Please try again.' };
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    updateProfile,
    resetPassword
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
