import React from 'react';
import Header from './Header';
import Footer from './Footer';
import AccessibilityAudit from '../accessibility/AccessibilityAudit';
import ResponsivenessTest from '../testing/ResponsivenessTest';
import PerformanceMonitor from '../performance/PerformanceMonitor';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
}

const Layout: React.FC<LayoutProps> = ({ children, className = '' }) => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Skip to main content link for accessibility */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>
      <Header />
      <main id="main-content" className={`flex-grow pt-20 ${className}`} role="main">
        {children}
      </main>
      <Footer />
      <AccessibilityAudit />
      <ResponsivenessTest />
      <PerformanceMonitor />
    </div>
  );
};

export default Layout;
